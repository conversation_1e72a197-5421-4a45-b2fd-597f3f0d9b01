package com.asia.selfDiscipline.exceptionHandler;

import com.asia.selfDiscipline.common.Result;
import com.asia.selfDiscipline.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.sql.SQLIntegrityConstraintViolationException;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    /**
     * 处理数据重复异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public Result<Void> handleDuplicateKeyException(DuplicateKeyException e) {
        log.warn("数据重复提交: {}", e.getMessage());
        return Result.error(Constants.DATA_DUPLICATION);
    }

    /**
     * 处理数据完整性异常
     *
     * @param e
     * @return
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public Result<Void> handleDataIntegrityViolationException(DataIntegrityViolationException e) {
        log.warn("数据库完整性约束异常: {}", e.getMessage());
        return Result.error(Constants.FOREIGN_KEY_ASSOCIATION_FAILED);
    }

    /**
     * 兜底异常处理
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常: ", e);
        return Result.error(Constants.THE_SYSTEM_IS_BUSY);
    }
}
