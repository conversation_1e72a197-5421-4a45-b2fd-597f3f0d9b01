package com.asia.selfDiscipline.config;

import com.asia.selfDiscipline.utils.JwtInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * <p>
 * 配置Spring MVC相关功能，主要负责拦截器的注册和路径配置。
 * 该配置类实现了WebMvcConfigurer接口，用于自定义Spring MVC的行为。
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    /**
     * JWT拦截器，用于验证用户身份
     */
    @Autowired
    private JwtInterceptor jwtInterceptor;

    /**
     * 添加拦截器配置
     * <p>
     * 配置JWT拦截器的拦截路径和排除路径。
     * 对所有/user/**路径进行JWT验证，但排除登录、注册等无需验证的接口。
     *
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(jwtInterceptor)
                // 拦截所有用户相关的接口
                .addPathPatterns()
                // 排除不需要JWT验证的路径
                .excludePathPatterns();
    }
}