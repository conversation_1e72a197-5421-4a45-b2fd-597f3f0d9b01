package com.asia.selfDiscipline.mapper.admin;

import com.asia.selfDiscipline.entity.DO.Staff;
import com.asia.selfDiscipline.entity.DTO.StaffLoginDto;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface StaffMapper extends BaseMapper<Staff> {

    @Select("select * from staff where username=#{username} and password=#{password}")
    Staff login(StaffLoginDto staffLoginDto);

    @Select("select * from staff where username=#{username}")
    Staff selectByUsername(String username);

    List<Staff> pageByName(QueryDto queryDto);
}