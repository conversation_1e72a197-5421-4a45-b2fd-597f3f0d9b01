package com.asia.selfDiscipline.mapper.admin;

import com.asia.selfDiscipline.entity.DO.FoodCategory;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface FoodCategoryMapper extends BaseMapper<FoodCategory> {


    List<FoodCategory> pageByName(QueryDto queryDto);
}