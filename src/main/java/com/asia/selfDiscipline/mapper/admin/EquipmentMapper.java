package com.asia.selfDiscipline.mapper.admin;

import com.asia.selfDiscipline.entity.DO.Equipment;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface EquipmentMapper extends BaseMapper<Equipment> {


    List<Equipment> pageByName(QueryDto queryDto);
}