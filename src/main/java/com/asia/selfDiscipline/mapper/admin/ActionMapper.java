package com.asia.selfDiscipline.mapper.admin;

import com.asia.selfDiscipline.entity.DO.Action;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface ActionMapper extends BaseMapper<Action> {


    List<Action> pageByName(QueryDto queryDto);

    @Select("select * from action where category_id=#{categoryId}")
    List<Action> queryByCategoryId(Integer categoryId);

    @Select("select * from action where equipment_id=#{equipmentId}")
    List<Action> queryByEquipmentId(Integer equipmentId);
}