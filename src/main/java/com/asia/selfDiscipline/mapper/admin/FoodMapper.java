package com.asia.selfDiscipline.mapper.admin;

import com.asia.selfDiscipline.entity.DO.Food;
import com.asia.selfDiscipline.entity.DO.Staff;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface FoodMapper extends BaseMapper<Food> {


    List<Food> pageByName(QueryDto queryDto);

    @Select("select * from food where category_id=#{categoryId}")
    List<Food> queryByCategoryId(Integer categoryId);
}