package com.asia.selfDiscipline.controller.admin;

import cn.hutool.core.collection.CollUtil;
import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.common.Result;
import com.asia.selfDiscipline.entity.DO.Action;
import com.asia.selfDiscipline.entity.DO.Equipment;
import com.asia.selfDiscipline.entity.DO.FoodCategory;
import com.asia.selfDiscipline.entity.DTO.FoodAddDto;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.service.admin.ActionService;
import com.asia.selfDiscipline.service.admin.EquipmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/equipment")
@Slf4j
public class EquipmentController {
    @Autowired
    private EquipmentService EquipmentService;
    @Autowired
    private ActionService actionService;

    /**
     * 添加器械
     *
     * @param Equipment
     * @return
     */
    @PostMapping
    public Result addEquipment(@RequestBody Equipment Equipment) {
        log.info("添加器械{}", Equipment);
        EquipmentService.save(Equipment);
        return Result.success();
    }

    @DeleteMapping
    public Result deleteEquipment(@RequestParam Integer id) {
        log.info("删除器械: {}", id);
        List<Action> list = actionService.queryByEquipmentId(id);
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("器械下有动作,无法删除");
        }
        EquipmentService.removeById(id);
        return Result.success();
    }

    /**
     * 修改器械
     *
     * @param equipment
     * @return
     */
    @PutMapping
    public Result updateEquipment(@RequestBody Equipment equipment) {
        log.info("修改器械: {}", equipment);
        EquipmentService.updateById(equipment);
        return Result.success();
    }


    @GetMapping
    public Result<PageResult> page(QueryDto queryDto) {
        log.info("分页器械: {}", queryDto);
        PageResult pageResult = EquipmentService.pageByName(queryDto);
        return Result.success(pageResult);
    }
}