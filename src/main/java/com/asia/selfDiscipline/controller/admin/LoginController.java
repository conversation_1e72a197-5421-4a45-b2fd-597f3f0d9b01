package com.asia.selfDiscipline.controller.admin;

import cn.hutool.core.util.ObjectUtil;
import com.asia.selfDiscipline.common.Result;
import com.asia.selfDiscipline.entity.DO.Staff;
import com.asia.selfDiscipline.entity.DTO.StaffLoginDto;
import com.asia.selfDiscipline.service.admin.LoginService;
import com.asia.selfDiscipline.utils.JwtUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/login")
@Slf4j
public class LoginController {
    @Autowired
    private LoginService LoginService;

    /**
     * @param staffLoginDto 接受类
     * @return 后端统一返回结果封装类
     */
    @PostMapping
    public Result login(@RequestBody StaffLoginDto staffLoginDto) {
        log.info("登录接口实体:staffLogin: {}", staffLoginDto);
        //获取员工信息
        Staff staff = LoginService.login(staffLoginDto);
        //判断员工信息是否存在
        if (ObjectUtil.isNull(staff)){
            return Result.error("用户名或密码错误");
        }
        //判断员工账号是否被禁用
        if (staff.getIsBanned()){
            return Result.error("账号已被禁用");
        }
        //生成JWT令牌
        String token = JwtUtils.generateToken(staff.getStaffId());
        log.info("登录成功，生成的令牌: {}", token);
        return Result.success(token);
    }


}