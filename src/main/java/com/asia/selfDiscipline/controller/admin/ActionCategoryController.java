package com.asia.selfDiscipline.controller.admin;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.common.Result;

import com.asia.selfDiscipline.entity.DO.Action;
import com.asia.selfDiscipline.entity.DO.ActionCategory;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.service.admin.ActionCategoryService;
import com.asia.selfDiscipline.service.admin.ActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/sportCategory")
@Slf4j
public class ActionCategoryController {
    @Autowired
    private ActionCategoryService ActionCategoryService;
    @Autowired
    private ActionService actionService;

    /**
     * 添加运动分类
     *
     * @param SportCategory
     * @return
     */
    @PostMapping
    public Result addSportCategory(@RequestBody ActionCategory SportCategory) {
        log.info("添加动作分类{}", SportCategory);
        ActionCategoryService.save(SportCategory);
        return Result.success();
    }

    /**
     * 删除运动分类
     *
     * @param id
     * @return
     */
    @DeleteMapping
    public Result deleteSportCategory(@RequestParam Integer id) {
        log.info("删除动作分类: {}", id);
        List<Action> list = actionService.queryByCategoryId(id);
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("分类下有动作,无法删除");
        }
        ActionCategoryService.removeById(id);
        return Result.success();
    }

    /**
     * 修改运动分类
     *
     * @param actionCategory
     * @return
     */
    @PutMapping
    public Result updateSportCategory(@RequestBody ActionCategory actionCategory) {
        log.info("修改动作分类: {}", actionCategory);
        ActionCategoryService.updateById(actionCategory);
        return Result.success();
    }

    /**
     * 分页查询
     *
     * @param queryDto
     * @return
     */
    @GetMapping
    public Result<PageResult> page(QueryDto queryDto) {
        log.info("分页动作分类: {}", queryDto);
        PageResult pageResult = ActionCategoryService.pageByName(queryDto);
        return Result.success(pageResult);
    }

}