package com.asia.selfDiscipline.controller.admin;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.common.Result;
import com.asia.selfDiscipline.entity.DO.Action;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.service.admin.ActionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/action")
@Slf4j
public class ActionController {
    @Autowired
    private ActionService ActionService;

    /**
     * 添加动作
     *
     * @param Action
     * @return
     */
    @PostMapping
    public Result addAction(@RequestBody Action Action) {
        log.info("添加动作{}", Action);
        ActionService.save(Action);
        return Result.success();
    }

    /**
     * 删除动作
     *
     * @param id
     * @return
     */
    @DeleteMapping
    public Result deleteAction(@RequestParam Integer id) {
        log.info("删除动作: {}", id);
        //TODO 判断是否有计划包含
        ActionService.removeById(id);
        return Result.success();
    }

    /**
     * 修改动作
     *
     * @param Action
     * @return
     */
    @PutMapping
    public Result updateAction(@RequestBody Action Action) {
        log.info("修改器械: {}", Action);
        ActionService.updateById(Action);
        return Result.success();
    }

    /**
     * 分页查询
     * @param queryDto
     * @return
     */
    @GetMapping
    public Result<PageResult> page(QueryDto queryDto) {
        log.info("分页器械: {}", queryDto);
        PageResult pageResult = ActionService.pageByName(queryDto);
        return Result.success(pageResult);
    }

}