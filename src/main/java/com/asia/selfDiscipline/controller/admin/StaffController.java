package com.asia.selfDiscipline.controller.admin;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.common.Result;
import com.asia.selfDiscipline.entity.DO.Staff;
import com.asia.selfDiscipline.entity.DTO.StaffAddDto;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.service.admin.StaffService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
    @RequestMapping("/admin/staff")
@Slf4j
public class StaffController {
    @Autowired
    private StaffService staffService;

    /**
     * 添加员工
     *
     * @param staffAddDto
     * @return 后端统一返回结果封装类
     */
    @PostMapping
    public Result addStaff(@RequestBody StaffAddDto staffAddDto) {
        log.info("添加员工接口实体: {}", staffAddDto);
        //判断手机号格式是否正确
        if (!Validator.isMobile(staffAddDto.getUsername())) {
            return Result.error("手机号格式错误");
        }
        //判断员工姓名是否为空
        if (ObjectUtil.isNull(staffAddDto.getName())) {
            return Result.error("添加失败,员工姓名不能为空");
        }
        //判断员工账号是否存在
        if (!staffService.save(staffAddDto)) {
            return Result.error("添加失败,员工账号存在");
        }

        return Result.success();
    }

    /**
     * 删除员工
     *
     * @param id
     * @return 后端统一返回结果封装类
     */
    @DeleteMapping
    public Result deleteStaff(@RequestParam Integer id) {
        log.info("删除员工: {}", id);
        staffService.removeById(id);
        return Result.success();
    }

    /**
     * 修改员工
     *
     * @param staffAddDto
     * @return 后端统一返回结果封装类
     */
    @PutMapping
    public Result updateStaff(@RequestBody StaffAddDto staffAddDto) {
        log.info("修改员工实体: {}", staffAddDto);
        //获取员工信息
        Staff staff = staffService.getById(staffAddDto.getStaffId());
        //修改员工状态
        staffAddDto.setIsBanned(!staff.getIsBanned());
        BeanUtils.copyProperties(staffAddDto, staff);
        //修改员工
        log.info("修改员工: {}", staff);
        staffService.updateById(staff);
        return Result.success();
    }

    @GetMapping()
    public Result<PageResult> page( QueryDto queryDto) {
        log.info("分页查询员工实体: {}", queryDto);
        PageResult pageResult = staffService.pageByName(queryDto);
        return Result.success(pageResult);
    }

}