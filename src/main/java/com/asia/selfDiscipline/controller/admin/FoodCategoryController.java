package com.asia.selfDiscipline.controller.admin;

import cn.hutool.core.collection.CollUtil;
import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.common.Result;
import com.asia.selfDiscipline.entity.DO.Food;
import com.asia.selfDiscipline.entity.DO.FoodCategory;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.service.admin.FoodCategoryService;
import com.asia.selfDiscipline.service.admin.FoodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/admin/foodCategory")
@Slf4j
public class FoodCategoryController {
    @Autowired
    private FoodCategoryService FoodCategoryService;
    @Autowired
    private FoodService foodService;


    /**
     * @param foodCategory
     * @return 后端统一返回结果封装类
     */
    @PostMapping
    public Result addFoodCategory(@RequestBody FoodCategory foodCategory) {
        log.info("添加食品分类");
        FoodCategoryService.save(foodCategory);
        return Result.success();
    }

    /**
     * 删除食品分类
     *
     * @param id
     * @return
     */
    @DeleteMapping
    public Result deleteFoodCategory(@RequestParam Integer id) {
        log.info("删除食品分类: {}", id);
        List<Food> list = foodService.queryByCategoryId(id);
        if (CollUtil.isNotEmpty(list)) {
            return Result.error("分类下有食物,无法删除");
        }
        FoodCategoryService.removeById(id);
        return Result.success();
    }

    /**
     * 修改食品分类
     *
     * @param foodCategory
     * @return
     */
    @PutMapping
    public Result updateFoodCategory(@RequestBody FoodCategory foodCategory) {
        log.info("修改食品分类: {}", foodCategory);

        FoodCategoryService.updateById(foodCategory);
        return Result.success();
    }


    /**
     * 分页查询食品分类
     *
     * @param queryDto
     * @return
     */
    @GetMapping()
    public Result<PageResult> page(QueryDto queryDto) {
        log.info("分页食品分类: {}", queryDto);
        PageResult pageResult = FoodCategoryService.pageByName(queryDto);
        return Result.success(pageResult);
    }

}