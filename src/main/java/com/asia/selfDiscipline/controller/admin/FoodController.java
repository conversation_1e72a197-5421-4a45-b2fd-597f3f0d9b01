package com.asia.selfDiscipline.controller.admin;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.common.Result;
import com.asia.selfDiscipline.entity.DO.Food;
import com.asia.selfDiscipline.entity.DTO.FoodAddDto;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.service.admin.FoodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/admin/food")
@Slf4j
public class FoodController {
    @Autowired
    private FoodService FoodService;

    /**
     * 添加食品
     *
     * @param food
     * @return
     */
    @PostMapping
    public Result addFood(@RequestBody FoodAddDto food) {
        log.info("添加食品{}", food);
        FoodService.addFood(food);
        return Result.success();
    }

    /**
     * 删除食品
     *
     * @param id
     * @return
     */
    @DeleteMapping
    public Result deleteFood(@RequestParam Integer id) {
        log.info("删除食品: {}", id);
        FoodService.deleteById(id);
        return Result.success();
    }

    /**
     * 修改食品
     *
     * @param foodAddDto
     * @return
     */
    @PutMapping
    public Result updateFood(@RequestBody FoodAddDto foodAddDto) {
        log.info("修改食品: {}", foodAddDto);
        FoodService.updateById1(foodAddDto);
        return Result.success();
    }


    /**
     * 分页查询食品
     *
     * @param queryDto
     * @return
     */
    @GetMapping()
    public Result<PageResult> page(QueryDto queryDto) {
        log.info("分页食品: {}", queryDto);
        PageResult pageResult = FoodService.pageByName(queryDto);
        return Result.success(pageResult);
    }
}