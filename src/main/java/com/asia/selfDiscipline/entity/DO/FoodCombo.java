package com.asia.selfDiscipline.entity.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 食品套餐表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("food_combo")
public class FoodCombo {

    /**
     * 套餐ID
     */
    @TableId(value = "combo_id", type = IdType.AUTO)
    private Integer comboId;

    /**
     * 套餐名称
     */
    @TableField("combo_name")
    private String comboName;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;
}
