package com.asia.selfDiscipline.entity.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户饮食记录表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_food_record")
public class UserFoodRecord {

    /**
     * 记录ID
     */
    @TableId(value = "record_id", type = IdType.AUTO)
    private Integer recordId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 日期
     */
    @TableField("date")
    private LocalDate date;

    /**
     * 餐次类型
     */
    @TableField("meal_type")
    private MealTypeEnum mealType;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 餐次类型枚举
     */
    public enum MealTypeEnum {
        BREAKFAST("breakfast"),
        LUNCH("lunch"),
        DINNER("dinner"),
        SNACK("snack");

        private final String value;

        MealTypeEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
