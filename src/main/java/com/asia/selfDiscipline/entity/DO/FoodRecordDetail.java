package com.asia.selfDiscipline.entity.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 饮食记录详情表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("food_record_detail")
public class FoodRecordDetail {

    /**
     * 明细ID
     */
    @TableId(value = "detail_id", type = IdType.AUTO)
    private Integer detailId;

    /**
     * 记录ID
     */
    @TableField("record_id")
    private Integer recordId;

    /**
     * 食品ID
     */
    @TableField("food_id")
    private Integer foodId;

    /**
     * 重量(克)
     */
    @TableField("grams")
    private BigDecimal grams;

    /**
     * 计算出的卡路里
     */
    @TableField("calculated_calories")
    private BigDecimal calculatedCalories;
}
