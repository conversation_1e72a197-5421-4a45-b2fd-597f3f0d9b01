package com.asia.selfDiscipline.entity.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户训练记录表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_training_log")
public class UserTrainingLog {

    /**
     * 日志ID
     */
    @TableId(value = "log_id", type = IdType.AUTO)
    private Integer logId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 计划ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 训练日期
     */
    @TableField("date")
    private LocalDate date;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;
}
