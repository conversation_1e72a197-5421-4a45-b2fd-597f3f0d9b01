package com.asia.selfDiscipline.entity.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 运动器械表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("equipment")
public class Equipment {

    /**
     * 器械ID
     */
    @TableId(value = "equipment_id", type = IdType.AUTO)
    private Integer equipmentId;

    /**
     * 器械名称
     */
    @TableField("name")
    private String name;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;
}
