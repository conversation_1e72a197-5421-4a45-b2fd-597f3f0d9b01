package com.asia.selfDiscipline.entity.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 训练计划详情表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("training_plan_detail")
public class TrainingPlanDetail {

    /**
     * 明细ID
     */
    @TableId(value = "detail_id", type = IdType.AUTO)
    private Integer detailId;

    /**
     * 计划ID
     */
    @TableField("plan_id")
    private Integer planId;

    /**
     * 动作ID
     */
    @TableField("action_id")
    private Integer actionId;

    /**
     * 重量(kg)
     */
    @TableField("weight")
    private Integer weight;

    /**
     * 重复次数
     */
    @TableField("reps")
    private Integer reps;

    /**
     * 休息时间(秒)
     */
    @TableField("rest_seconds")
    private Integer restSeconds;

    /**
     * 是否完成
     */
    @TableField("is_completed")
    private Boolean isCompleted;
}
