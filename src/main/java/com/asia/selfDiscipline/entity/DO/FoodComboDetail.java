package com.asia.selfDiscipline.entity.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 食品套餐明细表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@TableName("food_combo_detail")
public class FoodComboDetail {

    /**
     * 明细ID
     */
    @TableId(value = "detail_id", type = IdType.AUTO)
    private Integer detailId;

    /**
     * 套餐ID
     */
    @TableField("combo_id")
    private Integer comboId;

    /**
     * 食品ID
     */
    @TableField("food_id")
    private Integer foodId;

    /**
     * 食品数量
     */
    @TableField("food_count")
    private Integer foodCount;
}
