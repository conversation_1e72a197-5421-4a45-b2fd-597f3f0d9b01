package com.asia.selfDiscipline.entity.DO;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 食品表实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("food")
public class Food {

    /**
     * 食品ID
     */
    @TableId(value = "food_id", type = IdType.AUTO)
    private Integer foodId;

    /**
     * 食品名称
     */
    @TableField("food_name")
    private String foodName;

    /**
     * 图片URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Integer categoryId;

    /**
     * 每100g碳水化合物含量
     */
    @TableField("carbs_per_100g")
    private BigDecimal carbsPer100g;

    /**
     * 每100g蛋白质含量
     */
    @TableField("protein_per_100g")
    private BigDecimal proteinPer100g;

    /**
     * 每100g脂肪含量
     */
    @TableField("fat_per_100g")
    private BigDecimal fatPer100g;

    /**
     * 每100g卡路里含量
     */
    @TableField("calories_per_100g")
    private BigDecimal caloriesPer100g;

    /**
     * 创建时间
     */
    @TableField("created_at")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField("updated_at")
    private LocalDateTime updatedAt;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    private Boolean isDeleted;
}
