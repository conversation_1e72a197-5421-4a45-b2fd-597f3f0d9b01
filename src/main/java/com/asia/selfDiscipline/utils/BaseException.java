package com.asia.selfDiscipline.utils;

/**
 * 基础异常类
 *
 * 继承自RuntimeException的自定义异常类，用于处理业务逻辑中的异常情况。
 * 该异常类会被全局异常处理器捕获并统一处理，返回友好的错误信息给前端。
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */
public class BaseException extends RuntimeException {

    /**
     * 默认构造方法
     */
    public BaseException() {
    }

    /**
     * 带错误消息的构造方法
     *
     * @param msg 错误消息
     */
    public BaseException(String msg) {
        super(msg);
    }

}
