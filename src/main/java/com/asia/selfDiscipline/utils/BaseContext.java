package com.asia.selfDiscipline.utils;

/**
 * 基础上下文工具类
 * <p>
 * 使用ThreadLocal存储当前线程的用户ID，确保在同一个请求线程中
 * 可以随时获取当前登录用户的ID信息，避免在方法间传递用户ID参数。
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */
public class BaseContext {

    /**
     * ThreadLocal变量，用于存储当前线程的用户ID
     * 每个线程都有自己独立的副本，确保线程安全
     */
    public static ThreadLocal<Long> threadLocal = new ThreadLocal<>();

    /**
     * 设置当前线程的用户ID
     *
     * @param id 用户ID
     */
    public static void setCurrentId(Long id) {
        threadLocal.set(id);
    }

    /**
     * 获取当前线程的用户ID
     *
     * @return 当前用户ID，如果未设置则返回null
     */
    public static Long getCurrentId() {
        return threadLocal.get();
    }

    /**
     * 移除当前线程的用户ID
     * <p>
     * 在请求结束时调用，避免内存泄漏
     */
    public static void removeCurrentId() {
        threadLocal.remove();
    }

}
