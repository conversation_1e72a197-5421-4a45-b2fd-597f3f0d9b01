package com.asia.selfDiscipline.common;

import lombok.Data;
import java.io.Serializable;

/**
 * 后端统一返回结果封装类
 *
 * 用于统一封装所有API接口的返回结果，包含状态码、消息和数据。
 * 提供了成功和失败的静态工厂方法，便于快速创建返回结果。
 *
 * @param <T> 返回数据的类型
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */
@Data
public class Result<T> implements Serializable {

    /**
     * 状态编码：1表示成功，0和其它数字表示失败
     */
    private Integer code;

    /**
     * 返回消息，通常用于错误信息或成功提示
     */
    private String msg;

    /**
     * 返回的具体数据
     */
    private T data;

    /**
     * 创建成功结果（无数据）
     *
     * @param <T> 数据类型
     * @return 成功的Result对象
     */
    public static <T> Result<T> success() {
        Result<T> result = new Result<T>();
        result.code = 1;
        return result;
    }

    /**
     * 创建成功结果（包含数据）
     *
     * @param <T> 数据类型
     * @param object 要返回的数据对象
     * @return 包含数据的成功Result对象
     */
    public static <T> Result<T> success(T object) {
        Result<T> result = new Result<T>();
        result.data = object;
        result.msg = "操作成功";
        result.code = 1;
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param <T> 数据类型
     * @param msg 错误消息
     * @return 失败的Result对象
     */
    public static <T> Result<T> error(String msg) {
        Result result = new Result();
        result.msg = msg;
        result.code = 0;
        return result;
    }

}
