package com.asia.selfDiscipline.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 分页结果封装类
 *
 * 用于封装分页查询的结果，包含总记录数和当前页的数据集合。
 * 该类实现了Serializable接口，支持序列化操作。
 *
 * <AUTHOR> Team
 * @version 1.0
 * @since 2024
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageResult implements Serializable {

    /**
     * 总记录数
     * 表示满足查询条件的所有记录的总数量
     */
    private long total;

    /**
     * 当前页数据集合
     * 包含当前页面的所有数据记录
     */
    private List records;

}
