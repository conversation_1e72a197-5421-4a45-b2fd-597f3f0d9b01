package com.asia.selfDiscipline.service.admin.impl;

import cn.hutool.core.util.ObjectUtil;
import com.asia.selfDiscipline.entity.DO.Staff;
import com.asia.selfDiscipline.entity.DTO.StaffLoginDto;
import com.asia.selfDiscipline.mapper.admin.StaffMapper;
import com.asia.selfDiscipline.service.admin.LoginService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LoginServiceImpl extends ServiceImpl<StaffMapper, Staff> implements LoginService {
    @Autowired
    private StaffMapper StaffMapper;

    @Override
    public Staff login(StaffLoginDto staffLoginDto) {
        Staff staff = StaffMapper.login(staffLoginDto);
        if (ObjectUtil.isNull(staff)) {
            return null;
        }
        log.info("员工信息: {}", staff);
        return staff;
    }
}