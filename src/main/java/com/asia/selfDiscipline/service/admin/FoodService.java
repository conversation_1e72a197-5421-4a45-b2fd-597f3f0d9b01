package com.asia.selfDiscipline.service.admin;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.Food;
import com.asia.selfDiscipline.entity.DTO.FoodAddDto;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface FoodService extends IService<Food> {

    PageResult pageByName(QueryDto queryDto);

    void addFood(FoodAddDto food);

    void deleteById(Integer id);

    void updateById1(FoodAddDto foodAddDto);

    List<Food> queryByCategoryId(Integer id);
}