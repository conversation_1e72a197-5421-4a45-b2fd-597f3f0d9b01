package com.asia.selfDiscipline.service.admin.impl;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.ActionCategory;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.mapper.admin.ActionCategoryMapper;
import com.asia.selfDiscipline.service.admin.ActionCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ActionCategoryServiceImpl extends ServiceImpl<ActionCategoryMapper, ActionCategory> implements ActionCategoryService {
    @Autowired
    private ActionCategoryMapper ActionCategoryMapper;


    @Override
    public PageResult pageByName(QueryDto queryDto) {
        PageHelper.startPage(queryDto.getPage(), queryDto.getPageSize());
        List<ActionCategory> list = ActionCategoryMapper.pageByName(queryDto);
        PageInfo<ActionCategory> info = new PageInfo<>(list);
        PageResult pageResult = new PageResult(info.getTotal(), info.getList());
        return pageResult;
    }
}