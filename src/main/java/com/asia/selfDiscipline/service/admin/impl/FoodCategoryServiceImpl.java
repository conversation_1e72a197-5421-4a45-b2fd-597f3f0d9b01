package com.asia.selfDiscipline.service.admin.impl;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.FoodCategory;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.mapper.admin.FoodCategoryMapper;
import com.asia.selfDiscipline.service.admin.FoodCategoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class FoodCategoryServiceImpl extends ServiceImpl<FoodCategoryMapper, FoodCategory> implements FoodCategoryService {
    @Autowired
    private FoodCategoryMapper FoodCategoryMapper;


    /**
     * 分页查询食品分类
     * @param queryDto
     * @return 分页结果封装类
     */
    @Override
    public PageResult pageByName(QueryDto queryDto) {
        //设置分页插件
        PageHelper.startPage(queryDto.getPage(), queryDto.getPageSize());
        //查询
        List<FoodCategory> list = FoodCategoryMapper.pageByName(queryDto);
        //获取分页信息
        PageInfo<FoodCategory> pageInfo = new PageInfo<>(list);
        return new PageResult(pageInfo.getTotal(), pageInfo.getList());
    }
}