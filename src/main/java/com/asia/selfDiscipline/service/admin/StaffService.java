package com.asia.selfDiscipline.service.admin;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.Staff;
import com.asia.selfDiscipline.entity.DTO.StaffAddDto;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.baomidou.mybatisplus.extension.service.IService;

public interface StaffService extends IService<Staff> {

    boolean save(StaffAddDto staffAddDto);

    PageResult pageByName(QueryDto queryDto);
}