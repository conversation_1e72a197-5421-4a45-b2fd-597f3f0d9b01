package com.asia.selfDiscipline.service.admin.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.Staff;
import com.asia.selfDiscipline.entity.DTO.StaffAddDto;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.mapper.admin.StaffMapper;
import com.asia.selfDiscipline.service.admin.StaffService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class StaffServiceImpl extends ServiceImpl<StaffMapper, Staff> implements StaffService {
    @Autowired
    private StaffMapper StaffMapper;

    @Override
    public boolean save(StaffAddDto staffAddDto) {
        //判断员工账号是否存在
        Staff selectStaff = StaffMapper.selectByUsername(staffAddDto.getUsername());
        log.info("查询员工信息: {}", selectStaff);
        //员工账号存在
        if (!ObjectUtil.isNull(selectStaff)) {
            return false;
        }
        //员工账号不存在,添加员工
        Staff staff = new Staff();
        //将staffAdd中的属性复制到staff中
        BeanUtil.copyProperties(staffAddDto, staff);
        staff.setPassword("123");
        log.info("添加员工信息: {}", staff);
        StaffMapper.insert(staff);
        return true;
    }

    @Override
    public PageResult pageByName(QueryDto queryDto) {
        PageHelper.startPage(queryDto.getPage(), queryDto.getPageSize());
        List<Staff> list = StaffMapper.pageByName(queryDto);
        PageInfo<Staff> pageInfo = new PageInfo<>(list);
        return new PageResult(pageInfo.getTotal(), pageInfo.getList());
    }

}