package com.asia.selfDiscipline.service.admin.impl;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.Food;
import com.asia.selfDiscipline.entity.DO.Staff;
import com.asia.selfDiscipline.entity.DTO.FoodAddDto;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.mapper.admin.FoodMapper;
import com.asia.selfDiscipline.service.admin.FoodService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class FoodServiceImpl extends ServiceImpl<FoodMapper, Food> implements FoodService {
    @Autowired
    private FoodMapper FoodMapper;

    /**
     * 添加食品
     *
     * @param foodAddDto
     */
    @Override
    public void addFood(FoodAddDto foodAddDto) {
        //获取营养成分,计算卡路里
        double carbsPer100g = foodAddDto.getCarbsPer100g();
        double proteinPer100g = foodAddDto.getProteinPer100g();
        double fatPer100g = foodAddDto.getFatPer100g();
        double caloriesPer100g = carbsPer100g * 4 + proteinPer100g * 4 + fatPer100g * 9;
        Food food = new Food();
        //复制属性
        BeanUtils.copyProperties(foodAddDto, food);
        //设置卡路里
        food.setCaloriesPer100g(caloriesPer100g);
        log.info("添加食品: {}", food);
        FoodMapper.insert(food);
    }

    /**
     * 删除食品
     *
     * @param id
     */
    @Override
    public void deleteById(Integer id) {
        Food food = FoodMapper.selectById(id);
        food.setIsDeleted(true);
        FoodMapper.updateById(food);
    }

    /**
     * 根据分类ID查询食品
     *
     * @param categoryId
     * @return
     */
    @Override
    public List<Food> queryByCategoryId(Integer categoryId) {
        List<Food> list = FoodMapper.queryByCategoryId(categoryId);
        log.info("根据分类ID查询食品: {}", list);
        return list;
    }

    /**
     * 修改食品
     *
     * @param foodAddDto
     */
    @Override
    public void updateById1(FoodAddDto foodAddDto) {
        // //获取营养成分,计算卡路里
        double carbsPer100g = foodAddDto.getCarbsPer100g();
        double proteinPer100g = foodAddDto.getProteinPer100g();
        double fatPer100g = foodAddDto.getFatPer100g();
        double caloriesPer100g = carbsPer100g * 4 + proteinPer100g * 4 + fatPer100g * 9;
        Food food = new Food();
        //复制属性
        BeanUtils.copyProperties(foodAddDto, food);
        //设置卡路里
        food.setCaloriesPer100g(caloriesPer100g);
        log.info("修改食品: {}", food);
        FoodMapper.updateById(food);
    }

    @Override
    public PageResult pageByName(QueryDto queryDto) {
        PageHelper.startPage(queryDto.getPage(), queryDto.getPageSize());
        List<Food> list = FoodMapper.pageByName(queryDto);
        PageInfo<Food> pageInfo = new PageInfo<>(list);
        log.info("分页食品: {}", pageInfo);
        return new PageResult(pageInfo.getTotal(), pageInfo.getList());
    }
}