package com.asia.selfDiscipline.service.admin.impl;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.Equipment;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.mapper.admin.EquipmentMapper;
import com.asia.selfDiscipline.service.admin.EquipmentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EquipmentServiceImpl extends ServiceImpl<EquipmentMapper, Equipment> implements EquipmentService {
    @Autowired
    private EquipmentMapper EquipmentMapper;


    @Override
    public PageResult pageByName(QueryDto queryDto) {
        PageHelper.startPage(queryDto.getPage(), queryDto.getPageSize());
        List<Equipment> list = EquipmentMapper.pageByName(queryDto);
        PageInfo<Equipment>pageInfo =new PageInfo<>(list);
        PageResult pageResult = new PageResult(pageInfo.getTotal(), pageInfo.getList());
        log.info("分页器械: {}", pageResult);
        return pageResult;
    }
}