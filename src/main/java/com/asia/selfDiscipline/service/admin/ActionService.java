package com.asia.selfDiscipline.service.admin;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.Action;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface ActionService extends IService<Action> {

    PageResult pageByName(QueryDto queryDto);

    List<Action> queryByCategoryId(Integer categoryId);

    List<Action> queryByEquipmentId(Integer equipmentId);
}