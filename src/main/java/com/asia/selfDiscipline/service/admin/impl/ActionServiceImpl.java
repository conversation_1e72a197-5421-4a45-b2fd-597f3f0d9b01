package com.asia.selfDiscipline.service.admin.impl;

import com.asia.selfDiscipline.common.PageResult;
import com.asia.selfDiscipline.entity.DO.Action;
import com.asia.selfDiscipline.entity.DTO.QueryDto;
import com.asia.selfDiscipline.mapper.admin.ActionMapper;
import com.asia.selfDiscipline.service.admin.ActionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class ActionServiceImpl extends ServiceImpl<ActionMapper, Action> implements ActionService {
    @Autowired
    private ActionMapper ActionMapper;


    @Override
    public PageResult pageByName(QueryDto queryDto) {
        PageHelper.startPage(queryDto.getPage(), queryDto.getPageSize());
        List<Action> list = ActionMapper.pageByName(queryDto);
        PageInfo<Action> listPageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult(listPageInfo.getTotal(), listPageInfo.getList());
        log.info("动作分页: {}", pageResult);
        return pageResult;
    }

    /**
     * 根据器械ID查询动作
     *
     * @param categoryId
     * @return
     */
    @Override
    public List<Action> queryByCategoryId(Integer categoryId) {
        List<Action> list = ActionMapper.queryByCategoryId(categoryId);
        log.info("查询动作集合{}", list);
        return list;
    }

    /**
     * 根据器械ID查询动作
     *
     * @param equipmentId
     * @return
     */
    @Override
    public List<Action> queryByEquipmentId(Integer equipmentId) {
        List<Action> list = ActionMapper.queryByEquipmentId(equipmentId);
        log.info("查询动作集合{}", list);
        return list;
    }
}