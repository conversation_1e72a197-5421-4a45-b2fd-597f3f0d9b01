server:
  port: 8080
spring:
  datasource:
    username: root
    password: 123456
    url: ********************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
mybatis-plus:
  type-aliases-package: com.asia.selfDiscipline.entity.DO
  global-config:
    db-config:
      id-type: auto
  mapper-locations: classpath*:/mapper/**/*.xml
  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  level:
    com.asia.selfDiscipline: debug


