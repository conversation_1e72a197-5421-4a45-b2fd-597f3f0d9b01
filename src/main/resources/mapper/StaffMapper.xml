<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.asia.selfDiscipline.mapper.admin.StaffMapper">

    <select id="pageByName" resultType="com.asia.selfDiscipline.entity.DO.Staff">
        select * from staff
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>
</mapper>