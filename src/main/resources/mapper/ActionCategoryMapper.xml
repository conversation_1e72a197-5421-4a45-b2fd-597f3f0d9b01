<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.asia.selfDiscipline.mapper.admin.ActionCategoryMapper">
    <select id="pageByName" resultType="com.asia.selfDiscipline.entity.DO.ActionCategory">
        select * from action_category
        <where>
            <if test="name != null and name != ''">
                and name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>
</mapper>