<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.asia.selfDiscipline.mapper.admin.FoodCategoryMapper">
    <select id="pageByName" resultType="com.asia.selfDiscipline.entity.DO.FoodCategory">
        select * from food_category
        <where>
            <if test="name != null and name != ''">
                and category_name like concat('%', #{name}, '%')
            </if>
        </where>
    </select>
</mapper>